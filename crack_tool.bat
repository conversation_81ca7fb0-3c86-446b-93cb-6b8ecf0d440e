@echo off
chcp 65001 >nul
title 无线续杯绕过工具 v1.0
color 0B

echo.
echo ╔════════════════════════════════════════════════════════════╗
echo ║                  无线续杯验证绕过工具                      ║
echo ║                    自动化绕过脚本                          ║
echo ╚════════════════════════════════════════════════════════════╝
echo.

echo [INFO] 正在执行绕过操作...
echo.

REM 第一步：创建许可证文件
echo [1/6] 创建许可证文件...
echo LICENSED=1 > license.dat
echo ACTIVATED=TRUE >> license.dat
echo TRIAL=FALSE >> license.dat
echo REGISTERED=1 >> license.dat
echo EXPIRY=2099-12-31 >> license.dat
echo USER=CRACKED_USER >> license.dat
echo SERIAL=FFFFFFFFFFFFFFFF >> license.dat
echo VERSION=2.2.3 >> license.dat

echo LICENSED=1 > license.key
echo ACTIVATED=TRUE >> license.key
echo TRIAL=FALSE >> license.key
echo REGISTERED=1 >> license.key

echo [License] > config.ini
echo Status=Activated >> config.ini
echo Trial=False >> config.ini
echo Registered=True >> config.ini
echo Expiry=2099-12-31 >> config.ini
echo User=CrackedUser >> config.ini
echo Serial=FFFFFFFFFFFFFFFF >> config.ini
echo [Settings] >> config.ini
echo Version=2.2.3 >> config.ini
echo FirstRun=False >> config.ini
echo CheckUpdate=False >> config.ini
echo OnlineVerify=False >> config.ini

echo ✓ 许可证文件创建完成

REM 第二步：设置环境变量
echo [2/6] 设置环境变量...
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set LICENSE_VALID=1
set CRACK=1
set BYPASS=1
set WUXIAN_XUBEI_LICENSED=1
set UNLIMITED_REFILL_ACTIVATED=TRUE
echo ✓ 环境变量设置完成

REM 第三步：创建注册表项
echo [3/6] 创建注册表项...
reg add "HKCU\Software\WuxianXubei" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Trial" /t REG_SZ /d "FALSE" /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Expiry" /t REG_SZ /d "2099-12-31" /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Serial" /t REG_SZ /d "FFFFFFFFFFFFFFFF" /f >nul 2>&1

reg add "HKCU\Software\UnlimitedRefill" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\UnlimitedRefill" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1
reg add "HKCU\Software\UnlimitedRefill" /v "Trial" /t REG_SZ /d "FALSE" /f >nul 2>&1

echo ✓ 注册表项创建完成

REM 第四步：创建hosts文件条目（阻止在线验证）
echo [4/6] 配置网络绕过...
echo 127.0.0.1 license.server.com >> %WINDIR%\System32\drivers\etc\hosts 2>nul
echo 127.0.0.1 activation.server.com >> %WINDIR%\System32\drivers\etc\hosts 2>nul
echo 127.0.0.1 verify.server.com >> %WINDIR%\System32\drivers\etc\hosts 2>nul
echo ✓ 网络绕过配置完成

REM 第五步：创建启动脚本
echo [5/6] 创建启动脚本...
echo @echo off > start_cracked.bat
echo title 无线续杯 - 已激活版本 >> start_cracked.bat
echo echo 正在启动已激活的无线续杯... >> start_cracked.bat
echo set LICENSED=1 >> start_cracked.bat
echo set ACTIVATED=TRUE >> start_cracked.bat
echo set TRIAL=FALSE >> start_cracked.bat
echo set REGISTERED=1 >> start_cracked.bat
echo set WUXIAN_XUBEI_LICENSED=1 >> start_cracked.bat
echo start "" "无线续杯Win系统v2.2.3版本.exe" >> start_cracked.bat
echo exit >> start_cracked.bat
echo ✓ 启动脚本创建完成

REM 第六步：启动程序
echo [6/6] 启动程序...
echo.
echo ╔════════════════════════════════════════════════════════════╗
echo ║                      绕过完成！                            ║
echo ║                                                            ║
echo ║  所有绕过措施已应用，正在启动程序...                       ║
echo ║                                                            ║
echo ║  如果程序仍显示未激活，请尝试：                            ║
echo ║  1. 重启程序                                               ║
echo ║  2. 以管理员身份运行                                       ║
echo ║  3. 断网后启动程序                                         ║
echo ╚════════════════════════════════════════════════════════════╝
echo.

start "" "无线续杯Win系统v2.2.3版本.exe"

echo 程序已启动！
echo.
echo 如需再次启动，请使用 start_cracked.bat
echo.
pause
