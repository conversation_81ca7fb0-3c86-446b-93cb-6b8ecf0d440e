#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
软件验证绕过测试脚本
用于测试和分析软件的验证机制
"""

import os
import sys
import subprocess
import time
import winreg
import shutil
from pathlib import Path

class SoftwareBypassTester:
    def __init__(self, exe_path):
        self.exe_path = exe_path
        self.exe_name = Path(exe_path).stem
        self.backup_dir = "backup"
        
    def create_backup(self):
        """创建备份目录"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
            
    def test_registry_bypass(self):
        """测试注册表绕过方法"""
        print("=== 测试注册表绕过 ===")
        
        # 常见的软件注册表位置
        registry_paths = [
            r"HKEY_CURRENT_USER\Software",
            r"HKEY_LOCAL_MACHINE\SOFTWARE",
            r"HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        ]
        
        for path in registry_paths:
            try:
                # 查找可能相关的注册表项
                if "CURRENT_USER" in path:
                    root = winreg.HKEY_CURRENT_USER
                    subpath = path.replace("HKEY_CURRENT_USER\\", "")
                else:
                    root = winreg.HKEY_LOCAL_MACHINE  
                    subpath = path.replace("HKEY_LOCAL_MACHINE\\", "")
                    
                try:
                    key = winreg.OpenKey(root, subpath)
                    print(f"检查注册表路径: {path}")
                    # 这里可以添加更多的注册表操作
                    winreg.CloseKey(key)
                except:
                    pass
            except Exception as e:
                print(f"注册表访问错误: {e}")
                
    def test_file_bypass(self):
        """测试文件系统绕过方法"""
        print("=== 测试文件系统绕过 ===")
        
        # 检查常见的许可证文件位置
        common_license_files = [
            "license.dat",
            "license.key", 
            "activation.dat",
            "serial.key",
            "config.ini",
            "settings.dat"
        ]
        
        exe_dir = Path(self.exe_path).parent
        
        for filename in common_license_files:
            filepath = exe_dir / filename
            if filepath.exists():
                print(f"发现可能的许可证文件: {filepath}")
                # 备份原文件
                backup_path = Path(self.backup_dir) / filename
                shutil.copy2(filepath, backup_path)
                print(f"已备份到: {backup_path}")
                
    def test_time_bypass(self):
        """测试时间相关的绕过方法"""
        print("=== 测试时间绕过 ===")
        
        # 这里可以实现修改系统时间的逻辑
        # 注意：修改系统时间需要管理员权限
        print("提示：某些软件使用时间验证，可以尝试修改系统时间")
        
    def test_network_bypass(self):
        """测试网络验证绕过"""
        print("=== 测试网络验证绕过 ===")
        
        # 可以尝试阻断网络连接来绕过在线验证
        print("提示：如果软件需要在线验证，可以尝试断网运行")
        
    def create_crack_patch(self):
        """创建简单的补丁文件"""
        print("=== 创建测试补丁 ===")
        
        # 创建一个简单的批处理文件来启动程序
        batch_content = f'''@echo off
echo 启动程序测试...
echo 程序路径: {self.exe_path}

REM 设置环境变量（某些程序可能检查这些）
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE

REM 启动程序
start "" "{self.exe_path}"

echo 程序已启动
pause
'''
        
        with open("run_bypassed.bat", "w", encoding="gbk") as f:
            f.write(batch_content)
            
        print("已创建 run_bypassed.bat 启动脚本")
        
    def run_analysis(self):
        """运行完整分析"""
        print(f"开始分析程序: {self.exe_path}")
        print("=" * 50)
        
        self.create_backup()
        self.test_registry_bypass()
        self.test_file_bypass() 
        self.test_time_bypass()
        self.test_network_bypass()
        self.create_crack_patch()
        
        print("=" * 50)
        print("分析完成！")
        print("建议的测试步骤：")
        print("1. 运行 run_bypassed.bat 脚本")
        print("2. 检查是否有许可证文件可以修改")
        print("3. 尝试断网运行程序")
        print("4. 检查注册表中的相关项")

if __name__ == "__main__":
    exe_path = "无线续杯Win系统v2.2.3版本.exe"
    
    if not os.path.exists(exe_path):
        print(f"错误：找不到文件 {exe_path}")
        sys.exit(1)
        
    tester = SoftwareBypassTester(exe_path)
    tester.run_analysis()
