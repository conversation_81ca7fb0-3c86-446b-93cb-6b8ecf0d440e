#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证绕过效果测试工具
用于测试绕过是否真正有效
"""

import os
import sys
import subprocess
import winreg
import shutil
import time
from pathlib import Path

class BypassVerificationTester:
    def __init__(self):
        self.exe_name = "无线续杯Win系统v2.2.3版本.exe"
        self.backup_name = self.exe_name + ".original"
        self.test_results = []
        
    def print_header(self):
        print("=" * 70)
        print("           绕过效果验证测试工具")
        print("         测试绕过是否真正有效")
        print("=" * 70)
        print()
        
    def backup_current_state(self):
        """备份当前激活状态"""
        print("[备份] 备份当前激活状态...")
        
        backup_items = []
        
        # 备份许可证文件
        license_files = [
            "license.dat", "license.key", "config.ini", "license.json",
            "app.config", "settings.dat", "activation.key", "user.dat"
        ]
        
        backup_dir = "activation_backup"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
            
        for file in license_files:
            if os.path.exists(file):
                try:
                    shutil.copy2(file, os.path.join(backup_dir, file))
                    backup_items.append(f"文件: {file}")
                except:
                    pass
                    
        # 备份注册表项
        registry_paths = [
            "Software\\WuxianXubei",
            "Software\\UnlimitedRefill", 
            "Software\\WirelessRefill",
            "Software\\XuBei",
            "Software\\Refill",
            "Software\\WuXian",
            "Software\\续杯"
        ]
        
        reg_backup_file = os.path.join(backup_dir, "registry_backup.reg")
        with open(reg_backup_file, 'w', encoding='utf-8') as f:
            f.write("Windows Registry Editor Version 5.00\n\n")
            
            for path in registry_paths:
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, path)
                    f.write(f"[HKEY_CURRENT_USER\\{path}]\n")
                    
                    i = 0
                    while True:
                        try:
                            name, value, type = winreg.EnumValue(key, i)
                            if type == winreg.REG_DWORD:
                                f.write(f'"{name}"=dword:{value:08x}\n')
                            elif type == winreg.REG_SZ:
                                f.write(f'"{name}"="{value}"\n')
                            i += 1
                        except WindowsError:
                            break
                    f.write("\n")
                    winreg.CloseKey(key)
                    backup_items.append(f"注册表: {path}")
                except:
                    pass
                    
        print(f"✓ 已备份 {len(backup_items)} 项激活数据到 {backup_dir}")
        return len(backup_items) > 0
        
    def clear_activation_data(self):
        """清除激活数据以测试绕过"""
        print("[清除] 清除现有激活数据...")
        
        cleared_items = []
        
        # 删除许可证文件
        license_files = [
            "license.dat", "license.key", "config.ini", "license.json",
            "app.config", "settings.dat", "activation.key", "user.dat"
        ]
        
        for file in license_files:
            if os.path.exists(file):
                try:
                    os.remove(file)
                    cleared_items.append(f"删除文件: {file}")
                except:
                    pass
                    
        # 删除注册表项
        registry_paths = [
            "Software\\WuxianXubei",
            "Software\\UnlimitedRefill",
            "Software\\WirelessRefill", 
            "Software\\XuBei",
            "Software\\Refill",
            "Software\\WuXian",
            "Software\\续杯"
        ]
        
        for path in registry_paths:
            try:
                winreg.DeleteKey(winreg.HKEY_CURRENT_USER, path)
                cleared_items.append(f"删除注册表: {path}")
            except:
                # 尝试删除子键
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, path, 0, winreg.KEY_ALL_ACCESS)
                    subkeys = []
                    i = 0
                    while True:
                        try:
                            subkey = winreg.EnumKey(key, i)
                            subkeys.append(subkey)
                            i += 1
                        except WindowsError:
                            break
                    
                    for subkey in subkeys:
                        try:
                            winreg.DeleteKey(key, subkey)
                        except:
                            pass
                    
                    winreg.CloseKey(key)
                    winreg.DeleteKey(winreg.HKEY_CURRENT_USER, path)
                    cleared_items.append(f"删除注册表: {path}")
                except:
                    pass
                    
        # 清除环境变量
        env_vars = [
            "LICENSED", "ACTIVATED", "TRIAL", "REGISTERED", "PREMIUM", "VIP",
            "WUXIAN_XUBEI_LICENSED", "UNLIMITED_REFILL_ACTIVATED"
        ]
        
        for var in env_vars:
            if var in os.environ:
                del os.environ[var]
                cleared_items.append(f"清除环境变量: {var}")
                
        print(f"✓ 已清除 {len(cleared_items)} 项激活数据")
        for item in cleared_items:
            print(f"  {item}")
            
        return len(cleared_items) > 0
        
    def test_program_without_bypass(self):
        """测试程序在没有绕过的情况下的状态"""
        print("[测试] 测试程序未绕过状态...")
        
        if not os.path.exists(self.exe_name):
            print("✗ 找不到程序文件")
            return False
            
        print("启动程序进行测试...")
        print("请观察程序是否显示为未激活状态")
        print("如果显示试用版、要求激活等，说明清除成功")
        
        try:
            # 启动程序
            process = subprocess.Popen([self.exe_name], shell=True)
            print("✓ 程序已启动")
            
            # 等待用户确认
            print("\n" + "="*50)
            print("请检查程序状态:")
            print("1. 程序是否显示为未激活/试用版?")
            print("2. 是否有激活提示或限制?")
            print("3. 是否要求输入序列号?")
            print("="*50)
            
            while True:
                response = input("\n程序是否显示为未激活状态? (y/n): ").lower()
                if response in ['y', 'yes', '是']:
                    print("✓ 确认程序处于未激活状态")
                    self.test_results.append("未激活状态测试: 通过")
                    return True
                elif response in ['n', 'no', '否']:
                    print("✗ 程序仍显示为激活状态，清除可能不完整")
                    self.test_results.append("未激活状态测试: 失败")
                    return False
                else:
                    print("请输入 y 或 n")
                    
        except Exception as e:
            print(f"✗ 启动程序失败: {e}")
            return False
            
    def apply_bypass_and_test(self):
        """应用绕过并测试效果"""
        print("[绕过] 应用绕过方案...")
        
        # 重新创建许可证文件
        license_data = {
            "license.dat": [
                "LICENSED=1",
                "ACTIVATED=TRUE", 
                "TRIAL=FALSE",
                "REGISTERED=1",
                "EXPIRY=2099-12-31",
                "SERIAL=TEST-BYPASS-2024"
            ],
            "config.ini": [
                "[License]",
                "Status=Activated",
                "Trial=False",
                "Registered=True",
                "Expiry=2099-12-31"
            ]
        }
        
        for filename, content in license_data.items():
            with open(filename, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))
            print(f"  ✓ 创建 {filename}")
            
        # 设置环境变量
        env_vars = {
            "LICENSED": "1",
            "ACTIVATED": "TRUE",
            "TRIAL": "FALSE", 
            "REGISTERED": "1"
        }
        
        for var, value in env_vars.items():
            os.environ[var] = value
            print(f"  ✓ 设置 {var}={value}")
            
        # 创建注册表项
        try:
            key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, "Software\\WuxianXubei")
            winreg.SetValueEx(key, "Licensed", 0, winreg.REG_DWORD, 1)
            winreg.SetValueEx(key, "Activated", 0, winreg.REG_SZ, "TRUE")
            winreg.CloseKey(key)
            print("  ✓ 创建注册表项")
        except Exception as e:
            print(f"  ✗ 创建注册表项失败: {e}")
            
        print("✓ 绕过方案已应用")
        
    def test_program_with_bypass(self):
        """测试程序在绕过后的状态"""
        print("[验证] 测试绕过效果...")
        
        print("重新启动程序进行验证...")
        
        try:
            # 启动程序
            process = subprocess.Popen([self.exe_name], shell=True)
            print("✓ 程序已启动")
            
            print("\n" + "="*50)
            print("请检查绕过效果:")
            print("1. 程序是否显示为已激活状态?")
            print("2. 是否没有试用限制?")
            print("3. 是否可以正常使用所有功能?")
            print("="*50)
            
            while True:
                response = input("\n绕过是否成功? 程序显示为已激活? (y/n): ").lower()
                if response in ['y', 'yes', '是']:
                    print("🎉 绕过验证成功!")
                    self.test_results.append("绕过效果测试: 成功")
                    return True
                elif response in ['n', 'no', '否']:
                    print("❌ 绕过验证失败")
                    self.test_results.append("绕过效果测试: 失败")
                    return False
                else:
                    print("请输入 y 或 n")
                    
        except Exception as e:
            print(f"✗ 启动程序失败: {e}")
            return False
            
    def restore_original_state(self):
        """恢复原始激活状态"""
        print("[恢复] 恢复原始激活状态...")
        
        backup_dir = "activation_backup"
        if not os.path.exists(backup_dir):
            print("✗ 找不到备份目录")
            return False
            
        restored_items = []
        
        # 恢复许可证文件
        for file in os.listdir(backup_dir):
            if file.endswith('.reg'):
                continue
            source = os.path.join(backup_dir, file)
            if os.path.exists(source):
                try:
                    shutil.copy2(source, file)
                    restored_items.append(f"恢复文件: {file}")
                except:
                    pass
                    
        # 恢复注册表
        reg_file = os.path.join(backup_dir, "registry_backup.reg")
        if os.path.exists(reg_file):
            try:
                subprocess.run(["regedit", "/s", reg_file], check=True)
                restored_items.append("恢复注册表项")
            except:
                print("  注意: 注册表恢复可能需要管理员权限")
                
        print(f"✓ 已恢复 {len(restored_items)} 项数据")
        return len(restored_items) > 0
        
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*70)
        print("                    测试报告")
        print("="*70)
        
        for result in self.test_results:
            print(f"  {result}")
            
        print("\n测试结论:")
        if "绕过效果测试: 成功" in self.test_results:
            print("🎉 绕过工具验证成功! 可以有效绕过软件验证")
        else:
            print("❌ 绕过工具可能无效或需要改进")
            
        print("="*70)
        
    def run_verification_test(self):
        """运行完整的验证测试"""
        self.print_header()
        
        print("此工具将测试绕过是否真正有效")
        print("测试过程:")
        print("1. 备份当前激活状态")
        print("2. 清除激活数据")
        print("3. 测试程序未激活状态")
        print("4. 应用绕过方案")
        print("5. 测试绕过效果")
        print("6. 恢复原始状态")
        print()
        
        confirm = input("是否继续测试? 这将临时清除激活数据 (y/n): ")
        if confirm.lower() not in ['y', 'yes', '是']:
            print("测试已取消")
            return
            
        try:
            # 步骤1: 备份
            if not self.backup_current_state():
                print("备份失败，测试终止")
                return
                
            # 步骤2: 清除
            if not self.clear_activation_data():
                print("清除失败，测试终止")
                return
                
            # 步骤3: 测试未激活状态
            print("\n请关闭程序后按回车继续...")
            input()
            
            if not self.test_program_without_bypass():
                print("未激活状态测试失败")
                
            # 步骤4: 应用绕过
            print("\n请关闭程序后按回车继续...")
            input()
            
            self.apply_bypass_and_test()
            
            # 步骤5: 测试绕过效果
            if not self.test_program_with_bypass():
                print("绕过效果测试失败")
                
            # 步骤6: 恢复原始状态
            print("\n请关闭程序后按回车继续恢复...")
            input()
            
            self.restore_original_state()
            
            # 生成报告
            self.generate_test_report()
            
        except KeyboardInterrupt:
            print("\n测试被中断")
            print("正在恢复原始状态...")
            self.restore_original_state()
        except Exception as e:
            print(f"\n测试过程中发生错误: {e}")
            print("正在恢复原始状态...")
            self.restore_original_state()
            
        print("\n测试完成!")
        input("按回车键退出...")

if __name__ == "__main__":
    tester = BypassVerificationTester()
    tester.run_verification_test()
