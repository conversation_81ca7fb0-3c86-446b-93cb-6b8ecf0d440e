@echo off
chcp 65001 >nul
title 无线续杯一键绕过工具
color 0A

cls
echo.
echo    ██╗    ██╗██╗   ██╗██╗  ██╗██╗ █████╗ ███╗   ██╗
echo    ██║    ██║██║   ██║╚██╗██╔╝██║██╔══██╗████╗  ██║
echo    ██║ █╗ ██║██║   ██║ ╚███╔╝ ██║███████║██╔██╗ ██║
echo    ██║███╗██║██║   ██║ ██╔██╗ ██║██╔══██║██║╚██╗██║
echo    ╚███╔███╔╝╚██████╔╝██╔╝ ██╗██║██║  ██║██║ ╚████║
echo     ╚══╝╚══╝  ╚═════╝ ╚═╝  ╚═╝╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝
echo.
echo                    无线续杯一键绕过工具
echo                      版本: v3.0 Final
echo.
echo ════════════════════════════════════════════════════════════
echo.

echo [INFO] 正在执行一键绕过...
echo.

REM 快速绕过 - 所有方法一次性应用
echo ▶ 应用所有绕过方法...

REM 环境变量
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set WUXIAN_XUBEI_LICENSED=1
set UNLIMITED_REFILL_ACTIVATED=TRUE
set BYPASS=1
set CRACK=1

REM 许可证文件
echo LICENSED=1 > license.dat
echo ACTIVATED=TRUE >> license.dat
echo TRIAL=FALSE >> license.dat
echo REGISTERED=1 >> license.dat
echo EXPIRY=2099-12-31 >> license.dat
echo SERIAL=FFFFFFFFFFFFFFFF >> license.dat

echo LICENSED=1 > license.key
echo ACTIVATED=TRUE >> license.key

echo [License] > config.ini
echo Status=Activated >> config.ini
echo Trial=False >> config.ini
echo Registered=True >> config.ini
echo Expiry=2099-12-31 >> config.ini

echo ACTIVATED=1 > settings.dat
echo TRIAL=0 >> settings.dat
echo LICENSED=1 >> settings.dat

REM 注册表
reg add "HKCU\Software\WuxianXubei" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Trial" /t REG_SZ /d "FALSE" /f >nul 2>&1
reg add "HKCU\Software\UnlimitedRefill" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1

echo ✓ 绕过完成！
echo.

echo ▶ 启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"

echo.
echo ════════════════════════════════════════════════════════════
echo                        绕过成功！
echo ════════════════════════════════════════════════════════════
echo.
echo 程序已启动，验证应该已被绕过。
echo.
echo 如果仍显示未激活，请：
echo 1. 重启程序
echo 2. 以管理员身份运行此脚本
echo 3. 断网后重新启动程序
echo.
echo 按任意键退出...
pause >nul
