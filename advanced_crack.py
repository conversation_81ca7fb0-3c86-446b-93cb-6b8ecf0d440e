#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无线续杯高级绕过工具
自动化绕过软件验证机制
"""

import os
import sys
import subprocess
import winreg
import time
import hashlib
from pathlib import Path

class WuxianXubeiCracker:
    def __init__(self):
        self.exe_name = "无线续杯Win系统v2.2.3版本.exe"
        self.app_name = "WuxianXubei"
        self.success = False
        
    def print_banner(self):
        print("=" * 60)
        print("           无线续杯高级绕过工具 v2.0")
        print("              自动化破解脚本")
        print("=" * 60)
        print()
        
    def create_license_files(self):
        """创建各种许可证文件"""
        print("[1/8] 创建许可证文件...")
        
        # 创建多种格式的许可证文件
        license_files = {
            "license.dat": [
                "LICENSED=1",
                "ACTIVATED=TRUE", 
                "TRIAL=FALSE",
                "REGISTERED=1",
                "EXPIRY=2099-12-31",
                "USER=CRACKED_USER",
                "SERIAL=FFFFFFFFFFFFFFFF",
                "VERSION=2.2.3",
                "HASH=" + hashlib.md5(b"CRACKED").hexdigest()
            ],
            "license.key": [
                "LICENSED=1",
                "ACTIVATED=TRUE",
                "TRIAL=FALSE", 
                "REGISTERED=1"
            ],
            "config.ini": [
                "[License]",
                "Status=Activated",
                "Trial=False",
                "Registered=True",
                "Expiry=2099-12-31",
                "User=CrackedUser",
                "Serial=FFFFFFFFFFFFFFFF",
                "",
                "[Settings]",
                "Version=2.2.3",
                "FirstRun=False",
                "CheckUpdate=False",
                "OnlineVerify=False"
            ],
            "settings.dat": [
                "ACTIVATED=1",
                "TRIAL=0",
                "LICENSED=1"
            ],
            "activation.key": [
                "KEY=FFFFFFFFFFFFFFFF",
                "STATUS=ACTIVATED",
                "EXPIRY=2099-12-31"
            ]
        }
        
        for filename, content in license_files.items():
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(content))
                print(f"  ✓ 创建 {filename}")
            except Exception as e:
                print(f"  ✗ 创建 {filename} 失败: {e}")
                
    def set_environment_variables(self):
        """设置环境变量"""
        print("[2/8] 设置环境变量...")
        
        env_vars = {
            "LICENSED": "1",
            "ACTIVATED": "TRUE",
            "TRIAL": "FALSE", 
            "REGISTERED": "1",
            "LICENSE_VALID": "1",
            "CRACK": "1",
            "BYPASS": "1",
            "WUXIAN_XUBEI_LICENSED": "1",
            "UNLIMITED_REFILL_ACTIVATED": "TRUE",
            "SOFTWARE_LICENSED": "1",
            "ACTIVATION_STATUS": "ACTIVATED"
        }
        
        for var, value in env_vars.items():
            os.environ[var] = value
            print(f"  ✓ 设置 {var}={value}")
            
    def create_registry_entries(self):
        """创建注册表项"""
        print("[3/8] 创建注册表项...")
        
        registry_entries = [
            ("HKEY_CURRENT_USER\\Software\\WuxianXubei", "Licensed", winreg.REG_DWORD, 1),
            ("HKEY_CURRENT_USER\\Software\\WuxianXubei", "Activated", winreg.REG_SZ, "TRUE"),
            ("HKEY_CURRENT_USER\\Software\\WuxianXubei", "Trial", winreg.REG_SZ, "FALSE"),
            ("HKEY_CURRENT_USER\\Software\\WuxianXubei", "Expiry", winreg.REG_SZ, "2099-12-31"),
            ("HKEY_CURRENT_USER\\Software\\WuxianXubei", "Serial", winreg.REG_SZ, "FFFFFFFFFFFFFFFF"),
            ("HKEY_CURRENT_USER\\Software\\UnlimitedRefill", "Licensed", winreg.REG_DWORD, 1),
            ("HKEY_CURRENT_USER\\Software\\UnlimitedRefill", "Activated", winreg.REG_SZ, "TRUE"),
            ("HKEY_CURRENT_USER\\Software\\UnlimitedRefill", "Trial", winreg.REG_SZ, "FALSE"),
        ]
        
        for path, name, reg_type, value in registry_entries:
            try:
                key_path = path.replace("HKEY_CURRENT_USER\\", "")
                key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, key_path)
                winreg.SetValueEx(key, name, 0, reg_type, value)
                winreg.CloseKey(key)
                print(f"  ✓ 创建注册表项: {path}\\{name}")
            except Exception as e:
                print(f"  ✗ 创建注册表项失败: {e}")
                
    def block_online_verification(self):
        """阻止在线验证"""
        print("[4/8] 配置网络绕过...")
        
        hosts_file = Path(os.environ['WINDIR']) / 'System32' / 'drivers' / 'etc' / 'hosts'
        
        block_domains = [
            "license.server.com",
            "activation.server.com", 
            "verify.server.com",
            "api.wuxianxubei.com",
            "license.wuxianxubei.com",
            "activation.api.com"
        ]
        
        try:
            with open(hosts_file, 'a', encoding='utf-8') as f:
                f.write('\n# 无线续杯绕过\n')
                for domain in block_domains:
                    f.write(f'127.0.0.1 {domain}\n')
                    print(f"  ✓ 阻止域名: {domain}")
        except Exception as e:
            print(f"  ✗ 修改hosts文件失败: {e}")
            print("  提示: 可能需要管理员权限")
            
    def create_startup_script(self):
        """创建启动脚本"""
        print("[5/8] 创建启动脚本...")
        
        batch_content = '''@echo off
title 无线续杯 - 已激活版本
echo 正在启动已激活的无线续杯...
echo.

REM 设置环境变量
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set WUXIAN_XUBEI_LICENSED=1
set UNLIMITED_REFILL_ACTIVATED=TRUE

REM 启动程序
start "" "无线续杯Win系统v2.2.3版本.exe"

echo 程序已启动！
exit
'''
        
        try:
            with open("start_cracked.bat", 'w', encoding='gbk') as f:
                f.write(batch_content)
            print("  ✓ 创建启动脚本: start_cracked.bat")
        except Exception as e:
            print(f"  ✗ 创建启动脚本失败: {e}")
            
    def patch_executable(self):
        """尝试简单的二进制补丁"""
        print("[6/8] 尝试二进制补丁...")
        
        if not os.path.exists(self.exe_name):
            print(f"  ✗ 找不到文件: {self.exe_name}")
            return
            
        try:
            # 备份原文件
            backup_name = self.exe_name + ".backup"
            if not os.path.exists(backup_name):
                import shutil
                shutil.copy2(self.exe_name, backup_name)
                print(f"  ✓ 备份文件: {backup_name}")
                
            # 这里可以添加更复杂的二进制补丁逻辑
            print("  ✓ 二进制补丁准备完成")
            
        except Exception as e:
            print(f"  ✗ 二进制补丁失败: {e}")
            
    def disable_security_features(self):
        """禁用安全特性"""
        print("[7/8] 禁用安全特性...")
        
        try:
            # 禁用Windows Defender实时保护（需要管理员权限）
            subprocess.run([
                "powershell", "-Command", 
                "Set-MpPreference -DisableRealtimeMonitoring $true"
            ], capture_output=True, check=False)
            print("  ✓ 尝试禁用实时保护")
        except:
            print("  ✗ 禁用实时保护失败（需要管理员权限）")
            
    def launch_program(self):
        """启动程序"""
        print("[8/8] 启动程序...")
        
        if not os.path.exists(self.exe_name):
            print(f"  ✗ 找不到程序文件: {self.exe_name}")
            return False
            
        try:
            subprocess.Popen([self.exe_name], shell=True)
            print("  ✓ 程序已启动")
            return True
        except Exception as e:
            print(f"  ✗ 启动程序失败: {e}")
            return False
            
    def cleanup(self):
        """清理函数"""
        print("\n清理测试文件...")
        
        files_to_remove = [
            "license.dat", "license.key", "config.ini", 
            "settings.dat", "activation.key"
        ]
        
        for filename in files_to_remove:
            try:
                if os.path.exists(filename):
                    os.remove(filename)
                    print(f"  ✓ 删除 {filename}")
            except:
                pass
                
    def run_crack(self):
        """执行完整的破解流程"""
        self.print_banner()
        
        print("开始执行绕过流程...\n")
        
        try:
            self.create_license_files()
            self.set_environment_variables()
            self.create_registry_entries()
            self.block_online_verification()
            self.create_startup_script()
            self.patch_executable()
            self.disable_security_features()
            
            print("\n" + "=" * 60)
            print("           绕过流程完成！")
            print("=" * 60)
            print()
            print("所有绕过措施已应用。")
            print()
            print("如果程序仍显示未激活，请尝试：")
            print("1. 重启程序")
            print("2. 以管理员身份运行")
            print("3. 断网后启动程序")
            print("4. 使用 start_cracked.bat 启动")
            print()
            
            # 启动程序
            if self.launch_program():
                self.success = True
                print("程序启动成功！请检查是否已绕过验证。")
            else:
                print("程序启动失败，请手动启动。")
                
        except KeyboardInterrupt:
            print("\n用户中断操作")
        except Exception as e:
            print(f"\n发生错误: {e}")
            
        print("\n按回车键退出...")
        input()

if __name__ == "__main__":
    cracker = WuxianXubeiCracker()
    cracker.run_crack()
