@echo off
chcp 65001 >nul
title 软件验证绕过测试工具
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    软件验证绕过测试工具                      ║
echo ║                      仅供学习测试使用                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:MENU
echo 请选择测试方法：
echo.
echo [1] 环境变量绕过测试
echo [2] 配置文件绕过测试  
echo [3] 注册表绕过测试
echo [4] 时间绕过测试
echo [5] 网络绕过测试
echo [6] 组合绕过测试
echo [7] 查看程序信息
echo [8] 清理测试文件
echo [0] 退出
echo.
set /p choice=请输入选择 (0-8): 

if "%choice%"=="1" goto ENV_BYPASS
if "%choice%"=="2" goto FILE_BYPASS
if "%choice%"=="3" goto REG_BYPASS
if "%choice%"=="4" goto TIME_BYPASS
if "%choice%"=="5" goto NET_BYPASS
if "%choice%"=="6" goto COMBO_BYPASS
if "%choice%"=="7" goto INFO
if "%choice%"=="8" goto CLEANUP
if "%choice%"=="0" goto EXIT
goto MENU

:ENV_BYPASS
echo.
echo [环境变量绕过测试]
echo 设置常见的许可证环境变量...
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set LICENSE_VALID=1
set CRACK=1
echo 环境变量已设置，启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"
echo 程序已启动，请检查效果
pause
goto MENU

:FILE_BYPASS
echo.
echo [配置文件绕过测试]
echo 创建许可证文件...

echo LICENSED=1 > license.key
echo ACTIVATED=TRUE >> license.key
echo TRIAL=FALSE >> license.key

echo [License] > settings.ini
echo Status=Activated >> settings.ini
echo Valid=True >> settings.ini

echo 许可证文件已创建，启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"
echo 程序已启动，请检查效果
pause
goto MENU

:REG_BYPASS
echo.
echo [注册表绕过测试]
echo 注意：此操作需要管理员权限
echo 创建注册表项...
reg add "HKCU\Software\WuxianXubei" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Trial" /t REG_SZ /d "FALSE" /f >nul 2>&1
echo 注册表项已创建，启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"
echo 程序已启动，请检查效果
pause
goto MENU

:TIME_BYPASS
echo.
echo [时间绕过测试]
echo 注意：此方法可能需要管理员权限
echo 当前系统时间：
date /t
time /t
echo.
echo 提示：某些软件使用系统时间进行验证
echo 可以尝试将系统时间设置为较早的日期
echo 或者使用时间冻结工具
echo.
start "" "无线续杯Win系统v2.2.3版本.exe"
pause
goto MENU

:NET_BYPASS
echo.
echo [网络绕过测试]
echo 断开网络连接可能绕过在线验证...
echo 建议：
echo 1. 断开网络连接
echo 2. 启动程序
echo 3. 如果程序正常运行，说明可能存在在线验证
echo.
start "" "无线续杯Win系统v2.2.3版本.exe"
pause
goto MENU

:COMBO_BYPASS
echo.
echo [组合绕过测试]
echo 使用多种方法组合测试...

REM 设置环境变量
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1

REM 创建配置文件
echo [License] > app.config
echo Status=Activated >> app.config
echo Valid=True >> app.config
echo Expiry=2099-12-31 >> app.config

REM 创建许可证文件
echo VALID=1 > license.dat
echo ACTIVATED=TRUE >> license.dat

REM 注册表设置
reg add "HKCU\Software\Test" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1

echo 所有绕过方法已应用，启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"
echo 程序已启动，请检查效果
pause
goto MENU

:INFO
echo.
echo [程序信息]
if exist "无线续杯Win系统v2.2.3版本.exe" (
    echo 文件名: 无线续杯Win系统v2.2.3版本.exe
    dir "无线续杯Win系统v2.2.3版本.exe" | findstr "无线续杯"
) else (
    echo 错误：找不到程序文件
)
echo.
echo 当前目录文件：
dir /b
pause
goto MENU

:CLEANUP
echo.
echo [清理测试文件]
echo 删除测试过程中创建的文件...
del license.key >nul 2>&1
del license.dat >nul 2>&1
del settings.ini >nul 2>&1
del app.config >nul 2>&1
reg delete "HKCU\Software\WuxianXubei" /f >nul 2>&1
reg delete "HKCU\Software\Test" /f >nul 2>&1
echo 清理完成
pause
goto MENU

:EXIT
echo.
echo 感谢使用！
echo 提醒：请仅将此工具用于合法的测试目的
pause
exit

