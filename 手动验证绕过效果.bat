@echo off
chcp 65001 >nul
title 验证绕过效果工具
color 0E

echo ========================================
echo        验证绕过效果工具
echo ========================================
echo.

echo 此工具将帮助您验证绕过是否真正有效
echo.

:MENU
echo 请选择验证方法：
echo.
echo [1] 临时清除激活数据测试
echo [2] 检查绕过文件是否生效
echo [3] 查看注册表激活项
echo [4] 检查环境变量
echo [5] 恢复激活数据
echo [0] 退出
echo.
set /p choice=请选择 (0-5): 

if "%choice%"=="1" goto CLEAR_TEST
if "%choice%"=="2" goto CHECK_FILES
if "%choice%"=="3" goto CHECK_REGISTRY
if "%choice%"=="4" goto CHECK_ENV
if "%choice%"=="5" goto RESTORE
if "%choice%"=="0" goto EXIT
goto MENU

:CLEAR_TEST
echo.
echo [临时清除测试]
echo 警告：此操作将临时清除激活数据以测试绕过效果
echo 建议先备份当前状态
echo.
set /p confirm=确认继续? (y/n): 
if /i not "%confirm%"=="y" goto MENU

echo.
echo 正在备份当前激活数据...
if not exist "test_backup" mkdir test_backup

REM 备份许可证文件
for %%f in (license.dat license.key config.ini license.json app.config settings.dat activation.key user.dat) do (
    if exist "%%f" (
        copy "%%f" test_backup\ >nul 2>&1
        echo 已备份: %%f
    )
)

echo.
echo 正在清除激活数据...

REM 删除许可证文件
for %%f in (license.dat license.key config.ini license.json app.config settings.dat activation.key user.dat) do (
    if exist "%%f" (
        del "%%f" >nul 2>&1
        echo 已删除: %%f
    )
)

REM 删除注册表项
reg delete "HKCU\Software\WuxianXubei" /f >nul 2>&1
reg delete "HKCU\Software\UnlimitedRefill" /f >nul 2>&1
reg delete "HKCU\Software\WirelessRefill" /f >nul 2>&1

echo.
echo ========================================
echo 激活数据已清除！
echo.
echo 现在请启动程序检查：
echo 1. 程序是否显示为未激活/试用版？
echo 2. 是否有激活提示或功能限制？
echo.
echo 如果程序显示未激活，说明清除成功
echo 然后可以测试绕过工具的效果
echo ========================================
echo.
pause
goto MENU

:CHECK_FILES
echo.
echo [检查绕过文件]
echo.

echo 检查许可证文件：
for %%f in (license.dat license.key config.ini license.json app.config settings.dat activation.key user.dat) do (
    if exist "%%f" (
        echo ✓ 存在: %%f
        echo   内容预览:
        type "%%f" | findstr /i "LICENSED ACTIVATED TRIAL" 2>nul
        echo.
    ) else (
        echo ✗ 不存在: %%f
    )
)

echo.
pause
goto MENU

:CHECK_REGISTRY
echo.
echo [检查注册表激活项]
echo.

echo 检查 WuxianXubei 注册表项：
reg query "HKCU\Software\WuxianXubei" 2>nul
if %errorlevel%==0 (
    echo ✓ WuxianXubei 注册表项存在
) else (
    echo ✗ WuxianXubei 注册表项不存在
)

echo.
echo 检查 UnlimitedRefill 注册表项：
reg query "HKCU\Software\UnlimitedRefill" 2>nul
if %errorlevel%==0 (
    echo ✓ UnlimitedRefill 注册表项存在
) else (
    echo ✗ UnlimitedRefill 注册表项不存在
)

echo.
pause
goto MENU

:CHECK_ENV
echo.
echo [检查环境变量]
echo.

echo 检查激活相关环境变量：
for %%v in (LICENSED ACTIVATED TRIAL REGISTERED PREMIUM VIP WUXIAN_XUBEI_LICENSED) do (
    if defined %%v (
        echo ✓ %%v = !%%v!
    ) else (
        echo ✗ %%v 未设置
    )
)

echo.
pause
goto MENU

:RESTORE
echo.
echo [恢复激活数据]
echo.

if not exist "test_backup" (
    echo ✗ 找不到备份目录 test_backup
    echo 无法恢复激活数据
    pause
    goto MENU
)

echo 正在恢复激活数据...

REM 恢复许可证文件
for %%f in (license.dat license.key config.ini license.json app.config settings.dat activation.key user.dat) do (
    if exist "test_backup\%%f" (
        copy "test_backup\%%f" . >nul 2>&1
        echo 已恢复: %%f
    )
)

REM 重新创建注册表项
reg add "HKCU\Software\WuxianXubei" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1

echo.
echo ✓ 激活数据已恢复
echo 现在可以正常使用程序了
echo.
pause
goto MENU

:EXIT
echo.
echo 验证工具使用说明：
echo.
echo 要验证绕过是否真正有效：
echo 1. 使用"临时清除激活数据测试"
echo 2. 启动程序确认显示为未激活
echo 3. 运行绕过工具 (ultimate_bypass.py)
echo 4. 再次启动程序检查是否变为已激活
echo 5. 使用"恢复激活数据"恢复原状态
echo.
echo 如果经过清除后，绕过工具能让程序重新激活，
echo 说明绕过工具是真正有效的！
echo.
pause
exit
