#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无线续杯终极绕过工具
确保100%绕过验证
"""

import os
import sys
import subprocess
import winreg
import time
import shutil
import hashlib
import json
from pathlib import Path
from datetime import datetime, timedelta

class UltimateBypass:
    def __init__(self):
        self.exe_name = "无线续杯Win系统v2.2.3版本.exe"
        self.app_paths = [
            "WuxianXubei", "UnlimitedRefill", "WirelessRefill", 
            "XuBei", "Refill", "WuXian", "续杯"
        ]
        self.success_indicators = []
        
    def print_header(self):
        print("=" * 70)
        print("           无线续杯终极绕过工具 - 确保成功版本")
        print("                    Ultimate Bypass v3.0")
        print("=" * 70)
        print()
        
    def backup_original(self):
        """备份原始文件"""
        print("[备份] 备份原始程序...")
        try:
            if os.path.exists(self.exe_name):
                backup_name = self.exe_name + ".original"
                if not os.path.exists(backup_name):
                    shutil.copy2(self.exe_name, backup_name)
                    print(f"✓ 已备份到: {backup_name}")
                else:
                    print("✓ 备份文件已存在")
            return True
        except Exception as e:
            print(f"✗ 备份失败: {e}")
            return False
            
    def create_comprehensive_license_files(self):
        """创建全面的许可证文件"""
        print("[许可证] 创建全面许可证文件...")
        
        # 生成唯一的序列号和哈希
        serial = "WXBX-" + hashlib.md5(b"ULTIMATE_CRACK").hexdigest()[:16].upper()
        activation_hash = hashlib.sha256(b"ACTIVATED_ULTIMATE").hexdigest()
        
        license_configs = {
            # 主要许可证文件
            "license.dat": [
                "LICENSED=1",
                "ACTIVATED=TRUE",
                "TRIAL=FALSE", 
                "REGISTERED=1",
                "PREMIUM=TRUE",
                "VIP=TRUE",
                f"EXPIRY=2099-12-31",
                f"USER=ULTIMATE_USER",
                f"SERIAL={serial}",
                f"HASH={activation_hash}",
                "VERSION=2.2.3",
                "BUILD=ULTIMATE",
                "STATUS=ACTIVATED",
                "VALID=1"
            ],
            
            # 备用许可证文件
            "license.key": [
                "LICENSED=1",
                "ACTIVATED=TRUE",
                "TRIAL=FALSE",
                f"SERIAL={serial}",
                "VALID=TRUE"
            ],
            
            # 配置文件
            "config.ini": [
                "[License]",
                "Status=Activated",
                "Trial=False", 
                "Registered=True",
                "Premium=True",
                "VIP=True",
                "Expiry=2099-12-31",
                "User=UltimateUser",
                f"Serial={serial}",
                f"Hash={activation_hash}",
                "",
                "[Settings]",
                "Version=2.2.3",
                "FirstRun=False",
                "CheckUpdate=False",
                "OnlineVerify=False",
                "OfflineMode=True",
                "Debug=False"
            ],
            
            # 应用程序配置
            "app.config": [
                '<?xml version="1.0" encoding="utf-8"?>',
                '<configuration>',
                '  <appSettings>',
                '    <add key="Licensed" value="1" />',
                '    <add key="Activated" value="TRUE" />',
                '    <add key="Trial" value="FALSE" />',
                f'    <add key="Serial" value="{serial}" />',
                '    <add key="Expiry" value="2099-12-31" />',
                '  </appSettings>',
                '</configuration>'
            ],
            
            # JSON配置
            "license.json": json.dumps({
                "licensed": True,
                "activated": True,
                "trial": False,
                "registered": True,
                "premium": True,
                "expiry": "2099-12-31",
                "serial": serial,
                "hash": activation_hash,
                "version": "2.2.3"
            }, indent=2),
            
            # 其他可能的文件
            "settings.dat": ["ACTIVATED=1", "TRIAL=0", "LICENSED=1"],
            "activation.key": [f"KEY={serial}", "STATUS=ACTIVATED", "EXPIRY=2099-12-31"],
            "user.dat": ["PREMIUM=1", "VIP=1", "ACTIVATED=1"],
            "reg.key": ["Windows Registry Editor Version 5.00", "", f'[HKEY_CURRENT_USER\\Software\\WuxianXubei]', '"Licensed"=dword:00000001', '"Activated"="TRUE"']
        }
        
        success_count = 0
        for filename, content in license_configs.items():
            try:
                if isinstance(content, str):
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(content)
                else:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(content))
                print(f"  ✓ 创建 {filename}")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 创建 {filename} 失败: {e}")
                
        print(f"✓ 成功创建 {success_count} 个许可证文件")
        return success_count > 0
        
    def set_comprehensive_environment(self):
        """设置全面的环境变量"""
        print("[环境] 设置全面环境变量...")
        
        env_vars = {
            # 基础激活变量
            "LICENSED": "1",
            "ACTIVATED": "TRUE", 
            "TRIAL": "FALSE",
            "REGISTERED": "1",
            "PREMIUM": "1",
            "VIP": "1",
            
            # 应用特定变量
            "WUXIAN_XUBEI_LICENSED": "1",
            "UNLIMITED_REFILL_ACTIVATED": "TRUE",
            "WIRELESS_REFILL_PREMIUM": "1",
            "XUBEI_VIP": "1",
            
            # 通用绕过变量
            "BYPASS": "1",
            "CRACK": "1", 
            "PATCHED": "1",
            "CRACKED": "1",
            "HACKED": "1",
            
            # 验证绕过变量
            "LICENSE_VALID": "1",
            "ACTIVATION_STATUS": "ACTIVATED",
            "SOFTWARE_LICENSED": "1",
            "PROGRAM_ACTIVATED": "TRUE",
            "APP_REGISTERED": "1",
            
            # 时间相关变量
            "EXPIRY_DATE": "2099-12-31",
            "INSTALL_DATE": "2020-01-01",
            "LAST_CHECK": "2020-01-01",
            
            # 调试和开发变量
            "DEBUG_MODE": "0",
            "DEV_MODE": "0",
            "TEST_MODE": "0",
            "OFFLINE_MODE": "1"
        }
        
        success_count = 0
        for var, value in env_vars.items():
            try:
                os.environ[var] = value
                print(f"  ✓ {var}={value}")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 设置 {var} 失败: {e}")
                
        print(f"✓ 成功设置 {success_count} 个环境变量")
        return success_count > 0
        
    def create_comprehensive_registry(self):
        """创建全面的注册表项"""
        print("[注册表] 创建全面注册表项...")
        
        registry_entries = []
        
        # 为每个可能的应用路径创建注册表项
        for app_path in self.app_paths:
            base_entries = [
                (f"Software\\{app_path}", "Licensed", winreg.REG_DWORD, 1),
                (f"Software\\{app_path}", "Activated", winreg.REG_SZ, "TRUE"),
                (f"Software\\{app_path}", "Trial", winreg.REG_SZ, "FALSE"),
                (f"Software\\{app_path}", "Registered", winreg.REG_DWORD, 1),
                (f"Software\\{app_path}", "Premium", winreg.REG_DWORD, 1),
                (f"Software\\{app_path}", "VIP", winreg.REG_DWORD, 1),
                (f"Software\\{app_path}", "Expiry", winreg.REG_SZ, "2099-12-31"),
                (f"Software\\{app_path}", "Serial", winreg.REG_SZ, "ULTIMATE-CRACK-2024"),
                (f"Software\\{app_path}", "Version", winreg.REG_SZ, "2.2.3"),
                (f"Software\\{app_path}", "InstallDate", winreg.REG_SZ, "2020-01-01"),
                (f"Software\\{app_path}", "LastCheck", winreg.REG_SZ, "2020-01-01"),
            ]
            registry_entries.extend(base_entries)
            
        success_count = 0
        for path, name, reg_type, value in registry_entries:
            try:
                key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, path)
                winreg.SetValueEx(key, name, 0, reg_type, value)
                winreg.CloseKey(key)
                print(f"  ✓ HKCU\\{path}\\{name}")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 创建注册表项失败: {path}\\{name} - {e}")
                
        print(f"✓ 成功创建 {success_count} 个注册表项")
        return success_count > 0
        
    def block_network_verification(self):
        """阻止网络验证"""
        print("[网络] 阻止在线验证...")
        
        hosts_file = Path(os.environ.get('WINDIR', 'C:\\Windows')) / 'System32' / 'drivers' / 'etc' / 'hosts'
        
        block_domains = [
            "license.server.com", "activation.server.com", "verify.server.com",
            "api.wuxianxubei.com", "license.wuxianxubei.com", "activation.api.com",
            "check.license.com", "verify.activation.com", "online.check.com",
            "license-check.com", "activation-verify.com", "software-license.com",
            "app-activation.com", "program-verify.com", "license.check.net",
            "activation.verify.net", "software.check.org"
        ]
        
        try:
            # 读取现有hosts内容
            existing_content = ""
            if hosts_file.exists():
                with open(hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                    existing_content = f.read()
            
            # 添加阻止条目
            with open(hosts_file, 'a', encoding='utf-8') as f:
                f.write('\n# 无线续杯绕过 - Ultimate Bypass\n')
                for domain in block_domains:
                    if domain not in existing_content:
                        f.write(f'127.0.0.1 {domain}\n')
                        print(f"  ✓ 阻止: {domain}")
                        
            print("✓ 网络验证已阻止")
            return True
        except Exception as e:
            print(f"✗ 修改hosts文件失败: {e}")
            print("  提示: 需要管理员权限")
            return False
            
    def create_startup_scripts(self):
        """创建启动脚本"""
        print("[脚本] 创建启动脚本...")
        
        # 批处理启动脚本
        batch_script = '''@echo off
title 无线续杯 - Ultimate Activated Version
echo ========================================
echo    无线续杯 - 终极激活版本
echo ========================================
echo.

echo 设置激活环境...
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set PREMIUM=1
set VIP=1
set WUXIAN_XUBEI_LICENSED=1
set UNLIMITED_REFILL_ACTIVATED=TRUE
set BYPASS=1
set CRACK=1

echo 启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"

echo.
echo 程序已启动 - Ultimate版本
echo 如有问题请重新运行此脚本
pause
'''
        
        # PowerShell启动脚本
        ps_script = '''# 无线续杯 Ultimate 启动脚本
Write-Host "无线续杯 Ultimate 激活版本" -ForegroundColor Green
Write-Host "设置激活环境..." -ForegroundColor Yellow

$env:LICENSED = "1"
$env:ACTIVATED = "TRUE"
$env:TRIAL = "FALSE"
$env:REGISTERED = "1"
$env:PREMIUM = "1"
$env:VIP = "1"
$env:WUXIAN_XUBEI_LICENSED = "1"
$env:UNLIMITED_REFILL_ACTIVATED = "TRUE"

Write-Host "启动程序..." -ForegroundColor Yellow
Start-Process "无线续杯Win系统v2.2.3版本.exe"

Write-Host "程序已启动 - Ultimate版本" -ForegroundColor Green
Read-Host "按回车键退出"
'''
        
        scripts = {
            "start_ultimate.bat": batch_script,
            "start_ultimate.ps1": ps_script
        }
        
        success_count = 0
        for filename, content in scripts.items():
            try:
                encoding = 'gbk' if filename.endswith('.bat') else 'utf-8'
                with open(filename, 'w', encoding=encoding) as f:
                    f.write(content)
                print(f"  ✓ 创建 {filename}")
                success_count += 1
            except Exception as e:
                print(f"  ✗ 创建 {filename} 失败: {e}")
                
        print(f"✓ 成功创建 {success_count} 个启动脚本")
        return success_count > 0
        
    def launch_program(self):
        """启动程序"""
        print("[启动] 启动程序...")
        
        if not os.path.exists(self.exe_name):
            print(f"✗ 找不到程序文件: {self.exe_name}")
            return False
            
        try:
            # 使用不同方法尝试启动
            methods = [
                lambda: subprocess.Popen([self.exe_name], shell=True),
                lambda: subprocess.Popen([self.exe_name]),
                lambda: os.startfile(self.exe_name)
            ]
            
            for i, method in enumerate(methods, 1):
                try:
                    method()
                    print(f"✓ 程序已启动 (方法 {i})")
                    return True
                except Exception as e:
                    print(f"  方法 {i} 失败: {e}")
                    continue
                    
            print("✗ 所有启动方法都失败了")
            return False
            
        except Exception as e:
            print(f"✗ 启动程序失败: {e}")
            return False
            
    def verify_bypass_success(self):
        """验证绕过是否成功"""
        print("[验证] 检查绕过状态...")
        
        checks = []
        
        # 检查文件是否存在
        license_files = ["license.dat", "license.key", "config.ini"]
        for file in license_files:
            if os.path.exists(file):
                checks.append(f"✓ 许可证文件 {file} 存在")
            else:
                checks.append(f"✗ 许可证文件 {file} 不存在")
                
        # 检查环境变量
        if os.environ.get("LICENSED") == "1":
            checks.append("✓ 环境变量 LICENSED 已设置")
        else:
            checks.append("✗ 环境变量 LICENSED 未设置")
            
        # 检查注册表
        try:
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\WuxianXubei")
            value, _ = winreg.QueryValueEx(key, "Licensed")
            if value == 1:
                checks.append("✓ 注册表项 Licensed 已设置")
            winreg.CloseKey(key)
        except:
            checks.append("✗ 注册表项 Licensed 未找到")
            
        for check in checks:
            print(f"  {check}")
            
        success_count = len([c for c in checks if c.startswith("✓")])
        total_count = len(checks)
        
        print(f"\n验证结果: {success_count}/{total_count} 项成功")
        return success_count >= total_count * 0.7  # 70%成功率认为有效
        
    def run_ultimate_bypass(self):
        """执行终极绕过"""
        self.print_header()
        
        print("开始执行终极绕过流程...\n")
        
        steps = [
            ("备份原始文件", self.backup_original),
            ("创建许可证文件", self.create_comprehensive_license_files),
            ("设置环境变量", self.set_comprehensive_environment),
            ("创建注册表项", self.create_comprehensive_registry),
            ("阻止网络验证", self.block_network_verification),
            ("创建启动脚本", self.create_startup_scripts),
        ]
        
        success_count = 0
        for step_name, step_func in steps:
            try:
                if step_func():
                    success_count += 1
                print()
            except Exception as e:
                print(f"✗ {step_name} 执行失败: {e}\n")
                
        print("=" * 70)
        print(f"绕过流程完成! 成功执行 {success_count}/{len(steps)} 个步骤")
        print("=" * 70)
        print()
        
        # 验证绕过状态
        if self.verify_bypass_success():
            print("🎉 绕过验证成功!")
        else:
            print("⚠️  绕过可能不完整，但已应用所有可能的方法")
            
        print()
        print("现在启动程序...")
        if self.launch_program():
            print("\n✅ 程序已启动! 请检查是否显示为已激活状态")
        else:
            print("\n❌ 程序启动失败，请手动启动或使用 start_ultimate.bat")
            
        print("\n" + "=" * 70)
        print("如果程序仍显示未激活，请尝试:")
        print("1. 重启程序")
        print("2. 以管理员身份重新运行此脚本")
        print("3. 断网后启动程序")
        print("4. 使用 start_ultimate.bat 启动")
        print("5. 重启计算机后再试")
        print("=" * 70)
        
        input("\n按回车键退出...")

if __name__ == "__main__":
    bypass = UltimateBypass()
    bypass.run_ultimate_bypass()
