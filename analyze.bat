@echo off
chcp 65001 >nul
echo 程序分析工具
echo ================

echo [1] 文件基本信息：
dir "无线续杯Win系统v2.2.3版本.exe"
echo.

echo [2] 文件属性：
attrib "无线续杯Win系统v2.2.3版本.exe"
echo.

echo [3] 检查是否为.NET程序：
findstr /i "mscoree" "无线续杯Win系统v2.2.3版本.exe" >nul 2>&1
if %errorlevel%==0 (
    echo 可能是.NET程序
) else (
    echo 不是.NET程序或检测失败
)
echo.

echo [4] 检查常见字符串：
echo 搜索许可证相关字符串...
findstr /i "license trial activate register" "无线续杯Win系统v2.2.3版本.exe" >nul 2>&1
if %errorlevel%==0 (
    echo 发现许可证相关字符串
) else (
    echo 未发现明显的许可证字符串
)
echo.

echo [5] 运行程序并监控：
echo 启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"
echo 程序已启动，请观察其行为
echo.

pause
