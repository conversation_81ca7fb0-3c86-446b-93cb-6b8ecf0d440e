@echo off
title Wu<PERSON>ian XuBei Crack Tool
color 0A

echo ========================================
echo    WuXian XuBei Crack Tool v1.0
echo ========================================
echo.

echo [INFO] Applying bypass methods...
echo.

REM Set environment variables
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set WUXIAN_XUBEI_LICENSED=1
set UNLIMITED_REFILL_ACTIVATED=TRUE
set BYPASS=1
set CRACK=1

echo [1/4] Environment variables set

REM Create license files
echo LICENSED=1 > license.dat
echo ACTIVATED=TRUE >> license.dat
echo TRIAL=FALSE >> license.dat
echo REGISTERED=1 >> license.dat
echo EXPIRY=2099-12-31 >> license.dat
echo SERIAL=FFFFFFFFFFFFFFFF >> license.dat

echo LICENSED=1 > license.key
echo ACTIVATED=TRUE >> license.key

echo [License] > config.ini
echo Status=Activated >> config.ini
echo Trial=False >> config.ini
echo Registered=True >> config.ini
echo Expiry=2099-12-31 >> config.ini

echo [2/4] License files created

REM Registry entries
reg add "HKCU\Software\WuxianXubei" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Trial" /t REG_SZ /d "FALSE" /f >nul 2>&1
reg add "HKCU\Software\UnlimitedRefill" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1

echo [3/4] Registry entries created

REM Launch program
echo [4/4] Launching program...
start "" "无线续杯Win系统v2.2.3版本.exe"

echo.
echo ========================================
echo           CRACK COMPLETED!
echo ========================================
echo.
echo Program launched with bypass applied.
echo.
echo If still showing as unactivated:
echo 1. Restart the program
echo 2. Run as administrator
echo 3. Disconnect internet and restart
echo.
pause
