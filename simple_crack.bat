@echo off
title Crack Tool

echo Applying bypass...

set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1

echo LICENSED=1 > license.dat
echo ACTIVATED=TRUE >> license.dat
echo TRIAL=FALSE >> license.dat

echo LICENSED=1 > license.key
echo ACTIVATED=TRUE >> license.key

reg add "HKCU\Software\WuxianXubei" /v "Licensed" /t REG_DWORD /d 1 /f >nul 2>&1
reg add "HKCU\Software\WuxianXubei" /v "Activated" /t REG_SZ /d "TRUE" /f >nul 2>&1

echo Bypass applied!
echo Starting program...

start "" "无线续杯Win系统v2.2.3版本.exe"

echo Done!
pause
