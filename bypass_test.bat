@echo off
chcp 65001 >nul
echo ========================================
echo 软件验证绕过测试脚本
echo ========================================
echo.

echo [1] 检查程序文件信息...
dir "无线续杯Win系统v2.2.3版本.exe"
echo.

echo [2] 创建备份目录...
if not exist backup mkdir backup
echo 备份目录已创建
echo.

echo [3] 检查常见许可证文件...
for %%f in (license.dat license.key activation.dat serial.key config.ini settings.dat) do (
    if exist "%%f" (
        echo 发现文件: %%f
        copy "%%f" backup\ >nul 2>&1
        echo 已备份: %%f
    )
)
echo.

echo [4] 设置环境变量绕过...
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
echo 环境变量已设置
echo.

echo [5] 创建启动脚本...
echo @echo off > run_with_bypass.bat
echo echo 使用绕过设置启动程序... >> run_with_bypass.bat
echo set LICENSED=1 >> run_with_bypass.bat
echo set ACTIVATED=TRUE >> run_with_bypass.bat
echo set TRIAL=FALSE >> run_with_bypass.bat
echo set REGISTERED=1 >> run_with_bypass.bat
echo start "" "无线续杯Win系统v2.2.3版本.exe" >> run_with_bypass.bat
echo echo 程序已启动 >> run_with_bypass.bat
echo pause >> run_with_bypass.bat
echo 启动脚本已创建: run_with_bypass.bat
echo.

echo [6] 检查注册表相关项...
echo 检查当前用户软件注册表...
reg query "HKCU\Software" /s /f "续杯" 2>nul
reg query "HKCU\Software" /s /f "license" 2>nul
echo.

echo [7] 尝试直接运行程序...
echo 正在启动程序进行测试...
start "" "无线续杯Win系统v2.2.3版本.exe"
echo.

echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 建议的绕过方法：
echo 1. 使用 run_with_bypass.bat 启动程序
echo 2. 检查是否生成了许可证文件
echo 3. 尝试修改系统时间
echo 4. 断网运行程序
echo 5. 检查程序目录下的配置文件
echo.
echo 按任意键继续...
pause >nul
