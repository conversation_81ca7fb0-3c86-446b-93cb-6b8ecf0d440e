@echo off
title 无线续杯 - 已激活版本
color 0A

echo ========================================
echo    无线续杯 - Ultimate 激活版本
echo ========================================
echo.

echo 正在设置激活环境...

REM 设置所有必要的环境变量
set LICENSED=1
set ACTIVATED=TRUE
set TRIAL=FALSE
set REGISTERED=1
set PREMIUM=1
set VIP=1
set WUXIAN_XUBEI_LICENSED=1
set UNLIMITED_REFILL_ACTIVATED=TRUE
set WIRELESS_REFILL_PREMIUM=1
set XUBEI_VIP=1
set BYPASS=1
set CRACK=1
set PATCHED=1
set CRACKED=1
set HACKED=1
set LICENSE_VALID=1
set ACTIVATION_STATUS=ACTIVATED
set SOFTWARE_LICENSED=1
set PROGRAM_ACTIVATED=TRUE
set APP_REGISTERED=1
set EXPIRY_DATE=2099-12-31
set INSTALL_DATE=2020-01-01
set LAST_CHECK=2020-01-01
set DEBUG_MODE=0
set DEV_MODE=0
set TEST_MODE=0
set OFFLINE_MODE=1

echo ✓ 激活环境已设置

echo.
echo 正在启动程序...
start "" "无线续杯Win系统v2.2.3版本.exe"

echo.
echo ========================================
echo        程序已启动 - Ultimate版本
echo ========================================
echo.
echo 程序应该显示为已激活状态
echo 如有问题请重新运行此脚本
echo.
echo 按任意键退出...
pause >nul
